# Go Boilerplate Project

A clean, production-ready Go boilerplate implementing Clean Architecture principles with PostgreSQL and Redis integration. This project provides a solid foundation for building scalable REST APIs with proper separation of concerns.

## 🚀 Features

- **Clean Architecture**: Domain-driven design with clear separation between layers
- **HTTP Server**: Echo framework for high-performance REST API
- **Database**: PostgreSQL integration with GORM ORM
- **Caching**: Redis integration for caching and session management
- **Configuration**: Environment-based configuration management
- **Logging**: Structured logging with multiple levels and file output
- **Health Check**: Built-in health check endpoint
- **CORS**: Cross-Origin Resource Sharing support

## 📋 Prerequisites

- **Go**: Version 1.24.4 or higher
- **PostgreSQL**: Version 12 or higher
- **Redis**: Version 6 or higher
- **Git**: For version control

## 🛠️ Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd go-boilerplate
```

### 2. Install Dependencies

```bash
go mod vendor
```

### 3. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp files/conf/env.example .env
```

Edit the `.env` file with your configuration:

```env
ENV=development
PORT=8000

DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_username
DB_PASSWORD=your_password

REDIS_ADDRESS=localhost:6379
```

### 4. Database Setup

Ensure PostgreSQL is running and create your database:

```sql
CREATE DATABASE your_database_name;
```

### 5. Redis Setup

Ensure Redis is running on the configured address.

## 🚀 Usage

### Running the Application

```bash
go run cmd/project-name/main.go
```

The server will start on the port specified in your `.env` file (default: 8000).

### Available Endpoints

- **Health Check**: `GET /v1/health`
  - Returns the application health status
  - Response: `{"message": "Success", "data": {"Message": "service is running"}}`

### Example API Call

```bash
curl http://localhost:8080/v1/health
```

## 📁 Project Structure

```
├── cmd/
│   └── project-name/           # Application entry point
│       └── main.go
├── common/                     # Shared utilities
│   ├── const/                  # Application constants
│   ├── errors/                 # Custom error definitions
│   ├── log/                    # Logging utilities
│   └── types/                  # Common type definitions
├── domain/                     # Domain layer (business logic)
│   └── health/                 # Health domain
├── files/
│   ├── asset/                  # Static assets
│   └── conf/                   # Configuration files
├── pkg/
│   └── lib/                    # Shared libraries
│       ├── config/             # Configuration management
│       ├── database/           # Database utilities
│       └── parse/              # Parsing utilities
├── server/                     # Server initialization
│   └── http/                   # HTTP server and routes
├── usecase/                    # Use case layer (application logic)
│   └── health/                 # Health use cases
├── go.mod                      # Go module definition
└── go.sum                      # Go module checksums
```

## ⚙️ Configuration

The application uses environment variables for configuration. All settings are defined in the `.env` file:

| Variable | Description | Example |
|----------|-------------|---------|
| `ENV` | Environment mode | `development`, `production` |
| `PORT` | Server port | `8080` |
| `DB_HOST` | Database host | `localhost` |
| `DB_PORT` | Database port | `5432` |
| `DB_NAME` | Database name | `myapp_db` |
| `DB_USER` | Database username | `postgres` |
| `DB_PASSWORD` | Database password | `password` |
| `REDIS_ADDRESS` | Redis server address | `localhost:6379` |

## 🔧 Development Setup

### Code Organization

This project follows Clean Architecture principles:

1. **Domain Layer** (`domain/`): Contains business entities and interfaces
2. **Use Case Layer** (`usecase/`): Contains application-specific business rules
3. **Infrastructure Layer** (`pkg/lib/`): Contains external concerns (database, config)
4. **Interface Layer** (`server/http/`): Contains controllers and HTTP handlers

### Adding New Features

1. **Create Domain**: Define your business entities in `domain/`
2. **Create Use Case**: Implement business logic in `usecase/`
3. **Add HTTP Handler**: Create endpoints in `server/http/`
4. **Register Routes**: Add routes to the router in `server/http/http.go`

### Database Migrations

To add database migrations, uncomment and modify the migration line in `pkg/lib/config/database.go`:

```go
// db.AutoMigrate(&model.Example{}) // for migration
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run tests for specific package
go test ./domain/health/
```

### Test Structure

- Unit tests are located alongside the code they test
- Test files follow the `*_test.go` naming convention
- Example: `domain/health/health_test.go`

## 📦 Deployment

### Building the Application

```bash
# Build for current platform
go build -o bin/app cmd/project-name/main.go

# Build for Linux (common for deployment)
GOOS=linux GOARCH=amd64 go build -o bin/app cmd/project-name/main.go
```

### Environment Variables for Production

Ensure these environment variables are set in your production environment:

- Set `ENV=production`
- Use secure database credentials
- Configure proper Redis connection
- Set appropriate `PORT` for your deployment platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Coding Standards

- Follow Go conventions and best practices
- Write tests for new functionality
- Update documentation as needed
- Use meaningful commit messages

## 🆘 Support

If you encounter any issues or have questions:

1. Check the existing issues in the repository
2. Create a new issue with detailed information
3. Provide steps to reproduce any bugs

## 🔗 Dependencies

### Main Dependencies

- **Echo**: High performance, extensible, minimalist Go web framework
- **GORM**: The fantastic ORM library for Golang
- **PostgreSQL Driver**: GORM PostgreSQL driver
- **Redis**: Go client for Redis
- **GoDotEnv**: Go port of Ruby's dotenv library
- **Gonfig**: Configuration library for Go

### Development Dependencies

- **Ginkgo & Gomega**: BDD testing framework for Go

---

**Happy Coding! 🎉**
