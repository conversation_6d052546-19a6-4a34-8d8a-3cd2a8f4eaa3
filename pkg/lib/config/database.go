package config

import (
	"fmt"

	constanta "github.com/company-name/project-name/common/const"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var db *gorm.DB
var err error

func InitDatabase() {
	config := GetConfig()
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s", config.DBHost, config.DBUser, config.DBPassword, config.DBName, config.DBPort)
	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
	})
	if err != nil {
		panic(fmt.Sprintf("DB Connection Error: %s", err.Error()))
	}

	if config.Env == constanta.EnvDevelopment {
		db.Logger = logger.Default
	}
	// db.AutoMigrate(&model.Example{}) // for migration

	fmt.Println("Connected to Database")
}

func DbManager() *gorm.DB {
	return db
}
