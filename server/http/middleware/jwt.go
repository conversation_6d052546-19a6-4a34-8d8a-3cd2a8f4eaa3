package middleware

import (
	"fmt"
	"net/http"
	"strings"

	constanta "github.com/company-name/project-name/common/const"
	"github.com/company-name/project-name/pkg/lib/config"
	"github.com/dgrijalva/jwt-go"
	"github.com/labstack/echo"
)

func JWTVerify(role []string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {

			req := c.Request()
			header := req.Header
			auth := header.Get("Authorization")

			if len(auth) <= 0 {
				return echo.NewHTTPError(http.StatusUnauthorized, constanta.EmptyAuth)
			}

			splitToken := strings.Split(auth, " ")
			if len(splitToken) < 2 {
				return echo.NewHTTPError(http.StatusUnauthorized, constanta.EmptyAuth)
			}

			if splitToken[0] != "Bearer" {
				return echo.NewHTTPError(http.StatusUnauthorized, constanta.InvalidAuth)
			}

			tokenStr := splitToken[1]
			token, err := jwt.ParseWithClaims(tokenStr, &JWTPayload{}, func(token *jwt.Token) (interface{}, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf(constanta.UnexpectedSigning, token.Header["alg"])
				}
				return []byte(config.GetConfig().JWTSecret), nil
			})

			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, err.Error())
			}

			if claims, ok := token.Claims.(*JWTPayload); token.Valid && ok {
				c.Set("token", token)
				c.Set("tokenStr", tokenStr)
				c.Set("id", claims.ID)
				c.Set("email", claims.Email)
				c.Set("roles", claims.Roles)

				// You can add user verification to database or redis here

				if len(role) > 0 {
					isValidAccountType := false
					for _, val := range role {
						isRoleValid := false
						for _, userRole := range claims.Roles {
							if userRole == val {
								isRoleValid = true
								break
							}
						}
						if isRoleValid {
							isValidAccountType = true
						}
					}

					if !isValidAccountType {
						return echo.NewHTTPError(http.StatusUnauthorized, `Unauthorize`)
					}
				}

				return next(c)
			} else if ve, ok := err.(*jwt.ValidationError); ok {
				var errorStr string
				if ve.Errors&jwt.ValidationErrorMalformed != 0 {
					errorStr = fmt.Sprintf("Invalid token format: %s", tokenStr)
				} else if ve.Errors&(jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
					errorStr = "Token has been expired"
				} else {
					errorStr = fmt.Sprintf("Token Parsing Error: %s", ve.Error())
				}
				return echo.NewHTTPError(http.StatusUnauthorized, errorStr)
			} else {
				return echo.NewHTTPError(http.StatusUnauthorized, "Unknown token error")
			}
		}
	}
}
