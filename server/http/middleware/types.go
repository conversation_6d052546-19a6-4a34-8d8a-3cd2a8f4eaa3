package middleware

import "github.com/dgrijalva/jwt-go"

type JWTPayload struct {
	ID          int64    `json:"id"`
	Email       string   `json:"email,omitempty"`
	PhoneNumber string   `json:"phone_number,omitempty"`
	FirstName   string   `json:"first_name,omitempty"`
	LastName    string   `json:"last_name,omitempty"`
	Status      string   `json:"status,omitempty"`
	Roles       []string `json:"roles,omitempty"`
	jwt.StandardClaims
}
