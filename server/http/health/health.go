package health

import (
	"context"
	"net/http"

	constanta "github.com/company-name/project-name/common/const"
	"github.com/company-name/project-name/common/log"
	"github.com/company-name/project-name/common/types"
	"github.com/company-name/project-name/server"
	"github.com/labstack/echo"
)

func Check(c echo.Context) error {
	var status int

	health, err := server.HealthUseCase.Health(context.Background())
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    health,
	}
	status = http.StatusOK
	log.LogInfo("[health.Check]", map[string]interface{}{"resp:": resp})

	return c.JSO<PERSON>(status, resp)
}
