package server

import (
	healthDmn "github.com/company-name/project-name/domain/health"
	"github.com/company-name/project-name/pkg/lib/config"
	healthusecase "github.com/company-name/project-name/usecase/health"
)

var (
	// usecases
	HealthUseCase       *healthusecase.HealthUseCase

	// domains
	healthDomain       healthDmn.HealthDomain
)

func Init(mode ...string) error {
	config.RedisInit()
	config.InitDatabase()
	InitProjectName()

	return nil
}

func InitProjectName() {
	healthDomain = healthDmn.InitHealthDomain(healthDmn.HealthResource{})

	healthDomain := healthusecase.Domains{
		HealthDomain: &healthDomain,
	}

	HealthUseCase = healthusecase.InitHealthUseCase(healthDomain)
}
