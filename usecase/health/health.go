package health

import (
	"context"

	constanta "github.com/company-name/project-name/common/const"
	ers "github.com/company-name/project-name/common/errors"
	"github.com/company-name/project-name/common/log"
)

func (uc *HealthUseCase) Health(ctx context.Context) (HealthCheck, error) {
	health, err := uc.health.GetHealth(ctx)
	if err != nil {
		return HealthCheck{}, log.LogError(err, nil)
	}

	if !health.IsHealth {
		err = ers.ErrServiceBroken
		return HealthCheck{}, log.LogError(err, nil)
	}

	resp := HealthCheck{
		Message: constanta.ServiceRunning,
	}

	return resp, nil
}
